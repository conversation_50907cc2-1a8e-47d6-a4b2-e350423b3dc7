import 'dart:ffi';

import 'package:json_annotation/json_annotation.dart';

part 'brand.g.dart';

@JsonSerializable()
class BrandPageResponse {
  final int code;
  final String msg;
  final BrandPageData data;

  BrandPageResponse({
    required this.code,
    required this.msg,
    required this.data,
  });

  factory BrandPageResponse.fromJson(Map<String, dynamic> json) =>
      _$BrandPageResponseFromJson(json);
}

@JsonSerializable()
class BrandPageData {
  final int total;
  final int size;
  final int pages;
  final int current;
  final List<BrandCategoryModel> list;

  BrandPageData({
    required this.total,
    required this.list,
    required this.size,
    required this.pages,
    required this.current,
  });

  factory BrandPageData.fromJson(Map<String, dynamic> json) =>
      _$BrandPageDataFromJson(json);
}

@JsonSerializable()
class BrandCategoryModel {
  final String id;
  @JsonKey(fromJson: _parseStringToInt)
  final int sort;
  final String name;
  final dynamic image; // Could be String? if needed
  final dynamic remark; // Could be String? if needed
  final List<BrandModel> brandList;

  BrandCategoryModel({
    required this.id,
    required this.sort,
    required this.name,
    this.image,
    this.remark,
    required this.brandList,
  });

  factory BrandCategoryModel.fromJson(Map<String, dynamic> json) =>
      _$BrandCategoryModelFromJson(json);
}

@JsonSerializable()
class BrandModel {
  final String id;
  @JsonKey(fromJson: _parseStringToInt)
  final String sort;
  final String name;
  final String? imageUrl;
  final String? imageUrl1;
  final String? bannerImageUrl;
  final dynamic description; // Could be String? if needed
  final String createDate;
  final String updateDate;
  @JsonKey(name: 'goodsCount')
  final String goodsCount;
  @JsonKey(name: 'maxCashbackRate')
  final String maxCashbackRate;

  BrandModel({
    required this.id,
    required this.sort,
    required this.name,
    this.imageUrl,
    this.imageUrl1,
    this.bannerImageUrl,
    this.description,
    required this.createDate,
    required this.updateDate,
    required this.goodsCount,
    required this.maxCashbackRate,
  });

  factory BrandModel.fromJson(Map<String, dynamic> json) =>
      _$BrandModelFromJson(json);
}

// Helper function for numeric fields
int _parseStringToInt(dynamic value) {
  if (value is int) return value;
  if (value is String) return int.tryParse(value) ?? 0;
  return 0;
}